package cn.iocoder.yudao.module.importer.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "邮件服务器 - 校验邮件合法性 Response VO")
@Data
public class ValidateEmailRespVO {

    @Schema(description = "是否合法", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean valid;

    @Schema(description = "错误信息", example = "收件人不存在")
    private String errorMessage;

    public static ValidateEmailRespVO success() {
        ValidateEmailRespVO respVO = new ValidateEmailRespVO();
        respVO.setValid(true);
        return respVO;
    }

    public static ValidateEmailRespVO fail(String errorMessage) {
        ValidateEmailRespVO respVO = new ValidateEmailRespVO();
        respVO.setValid(false);
        respVO.setErrorMessage(errorMessage);
        return respVO;
    }

}
