package cn.iocoder.yudao.module.importer.service.mailserver;

import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailRespVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ValidateEmailRespVO;
import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.mq.producer.ImporterProducer;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 邮件服务器服务实现类
 */
@Service
@Slf4j
public class MailServerServiceImpl implements MailServerService {

    @Resource
    private ImportTaskService importTaskService;

    @Resource
    private ImporterProducer importerProducer;

    private static final Pattern EMAIL_PATTERN = Pattern.compile("import-([a-zA-Z0-9\\-]+)@import\\.ledger\\.com");

    @Override
    public ValidateEmailRespVO validateEmail(String sender, String recipient) {
        try {
            // 1. 校验收件人格式是否为导入任务邮箱
            String taskId = extractTaskIdFromEmail(recipient);
            if (taskId == null) {
                return ValidateEmailRespVO.fail("收件人邮箱格式不正确，不是有效的导入任务邮箱");
            }

            // 2. 校验导入任务是否存在
            ImportTaskDO task = importTaskService.getByUniqueEmail(recipient);
            if (task == null) {
                return ValidateEmailRespVO.fail("导入任务不存在");
            }

            // 3. 校验任务状态是否为等待邮件
            if (!ImportTaskStatusEnum.AWAITING_EMAIL.getStatus().equals(task.getStatus())) {
                return ValidateEmailRespVO.fail("任务状态不正确，当前状态：" + task.getStatus());
            }

            // 4. 校验发件人是否为支持的邮件类型
            if (!isSupportedSender(sender)) {
                return ValidateEmailRespVO.fail("不支持的发件人邮箱：" + sender);
            }

            log.info("邮件校验通过: taskId={}, sender={}, recipient={}", taskId, sender, recipient);
            return ValidateEmailRespVO.success();

        } catch (Exception e) {
            log.error("邮件校验异常: sender={}, recipient={}", sender, recipient, e);
            return ValidateEmailRespVO.fail("邮件校验异常：" + e.getMessage());
        }
    }

    @Override
    public ReportEmailRespVO reportEmail(ReportEmailReqVO reqVO) {
        try {
            // 1. 再次校验邮件合法性（防止恶意调用）
            ValidateEmailRespVO validateResult = validateEmail(reqVO.getSender(), reqVO.getRecipient());
            if (!validateResult.getValid()) {
                return ReportEmailRespVO.fail("邮件校验失败：" + validateResult.getErrorMessage());
            }

            // 2. 推送到邮件预处理队列
            importerProducer.sendMailReportMessage(reqVO);

            log.info("邮件上报成功推送到队列: objectKey={}, sender={}, recipient={}, emailId={}", 
                    reqVO.getObjectKey(), reqVO.getSender(), reqVO.getRecipient(), reqVO.getEmailId());
            
            return ReportEmailRespVO.success();

        } catch (Exception e) {
            log.error("邮件上报处理异常: objectKey={}, sender={}, recipient={}", 
                    reqVO.getObjectKey(), reqVO.getSender(), reqVO.getRecipient(), e);
            return ReportEmailRespVO.fail("邮件上报处理异常：" + e.getMessage());
        }
    }

    /**
     * 从邮箱地址提取任务ID
     */
    private String extractTaskIdFromEmail(String email) {
        Matcher matcher = EMAIL_PATTERN.matcher(email);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    /**
     * 校验是否为支持的发件人
     */
    private boolean isSupportedSender(String sender) {
        if (sender == null) {
            return false;
        }
        
        String lowerSender = sender.toLowerCase();
        
        // 微信支付相关邮箱
        if (lowerSender.contains("tenpay.com") || 
            lowerSender.contains("weixin") || 
            lowerSender.contains("wechat")) {
            return true;
        }
        
        // 支付宝相关邮箱
        if (lowerSender.contains("alipay.com")) {
            return true;
        }
        
        return false;
    }

}
