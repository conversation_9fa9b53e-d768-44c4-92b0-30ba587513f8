package cn.iocoder.yudao.module.importer.controller.app;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailRespVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ValidateEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ValidateEmailRespVO;
import cn.iocoder.yudao.module.importer.service.mailserver.MailServerService;
import cn.iocoder.yudao.module.importer.service.security.SignatureVerificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "邮件服务器接口")
@RestController
@RequestMapping("/ledger/mail-server")
@Slf4j
public class MailServerController {

    @Resource
    private SignatureVerificationService signatureVerificationService;

    @Resource
    private MailServerService mailServerService;

    @PostMapping("/validate-email")
    @Operation(summary = "校验邮件合法性")
    public CommonResult<ValidateEmailRespVO> validateEmail(@Valid @RequestBody ValidateEmailReqVO reqVO) {
        // 验证签名
        String payload = buildPayload(reqVO.getTimestamp(), reqVO.getSender(), reqVO.getRecipient());
        if (!signatureVerificationService.verifySignature(reqVO.getSignature(), reqVO.getTimestamp(), payload)) {
            log.warn("邮件校验签名验证失败: sender={}, recipient={}", reqVO.getSender(), reqVO.getRecipient());
            return success(ValidateEmailRespVO.fail("签名验证失败"));
        }

        // 校验邮件合法性
        ValidateEmailRespVO respVO = mailServerService.validateEmail(reqVO.getSender(), reqVO.getRecipient());
        
        log.info("邮件合法性校验完成: sender={}, recipient={}, valid={}", 
                reqVO.getSender(), reqVO.getRecipient(), respVO.getValid());
        
        return success(respVO);
    }

    @PostMapping("/report-email")
    @Operation(summary = "上报邮件")
    public CommonResult<ReportEmailRespVO> reportEmail(@Valid @RequestBody ReportEmailReqVO reqVO) {
        // 验证签名
        String payload = buildPayload(reqVO.getTimestamp(), reqVO.getObjectKey(), reqVO.getSender(), 
                reqVO.getRecipient(), reqVO.getEmailId(), reqVO.getReceivedTime().toString());
        if (!signatureVerificationService.verifySignature(reqVO.getSignature(), reqVO.getTimestamp(), payload)) {
            log.warn("邮件上报签名验证失败: objectKey={}, sender={}, recipient={}", 
                    reqVO.getObjectKey(), reqVO.getSender(), reqVO.getRecipient());
            return success(ReportEmailRespVO.fail("签名验证失败"));
        }

        // 处理邮件上报
        ReportEmailRespVO respVO = mailServerService.reportEmail(reqVO);
        
        log.info("邮件上报处理完成: objectKey={}, sender={}, recipient={}, success={}", 
                reqVO.getObjectKey(), reqVO.getSender(), reqVO.getRecipient(), respVO.getSuccess());
        
        return success(respVO);
    }

    /**
     * 构建签名载荷
     */
    private String buildPayload(String timestamp, String... data) {
        StringBuilder payload = new StringBuilder(timestamp);
        for (String item : data) {
            payload.append("|").append(item);
        }
        return payload.toString();
    }

}
