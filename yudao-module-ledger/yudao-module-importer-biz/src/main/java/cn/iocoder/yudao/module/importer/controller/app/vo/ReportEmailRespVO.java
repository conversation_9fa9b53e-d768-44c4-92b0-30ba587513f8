package cn.iocoder.yudao.module.importer.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "邮件服务器 - 上报邮件 Response VO")
@Data
public class ReportEmailRespVO {

    @Schema(description = "是否成功", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private Boolean success;

    @Schema(description = "错误信息", example = "邮件处理失败")
    private String errorMessage;

    public static ReportEmailRespVO success() {
        ReportEmailRespVO respVO = new ReportEmailRespVO();
        respVO.setSuccess(true);
        return respVO;
    }

    public static ReportEmailRespVO fail(String errorMessage) {
        ReportEmailRespVO respVO = new ReportEmailRespVO();
        respVO.setSuccess(false);
        respVO.setErrorMessage(errorMessage);
        return respVO;
    }

}
