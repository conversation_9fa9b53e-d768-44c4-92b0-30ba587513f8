package cn.iocoder.yudao.module.importer.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

@Schema(description = "邮件服务器 - 校验邮件合法性 Request VO")
@Data
public class ValidateEmailReqVO {

    @Schema(description = "邮件服务器签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "abc123def456")
    @NotBlank(message = "签名不能为空")
    private String signature;

    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED, example = "1634567890000")
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @Schema(description = "发件人邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    @NotBlank(message = "发件人邮箱不能为空")
    @Email(message = "发件人邮箱格式不正确")
    private String sender;

    @Schema(description = "收件人邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    @NotBlank(message = "收件人邮箱不能为空")
    @Email(message = "收件人邮箱格式不正确")
    private String recipient;

}
