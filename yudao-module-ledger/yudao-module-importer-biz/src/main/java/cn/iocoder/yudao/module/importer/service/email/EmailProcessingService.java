package cn.iocoder.yudao.module.importer.service.email;

import cn.iocoder.yudao.module.importer.mq.message.MailReportMessage;

/**
 * 邮件处理服务接口
 */
public interface EmailProcessingService {

    /**
     * 处理原始邮件数据
     *
     * @param rawEmailData 原始邮件数据
     */
    void processRawEmail(String rawEmailData);

    /**
     * 处理上报的邮件
     *
     * @param message 邮件上报消息
     */
    void processReportedEmail(MailReportMessage message);

}