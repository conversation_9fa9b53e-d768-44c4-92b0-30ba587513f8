package cn.iocoder.yudao.module.importer.controller.app;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.importer.controller.app.vo.CreateImportTaskReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.CreateImportTaskRespVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReceiveRawEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.SubmitPasswordReqVO;
import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.mq.producer.ImporterProducer;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import cn.iocoder.yudao.module.importer.service.password.PasswordVerificationService;
import cn.iocoder.yudao.module.importer.service.security.SignatureVerificationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.importer.enums.ErrorCodeConstants.UNZIP_CODE_IS_WRONG;

@Tag(name = "内部接口 - 导入")
@RestController
@RequestMapping("/ledger/import")
@Slf4j
public class ImportController {

    @Resource
    private ImporterProducer importerProducer;

    @Resource
    private ImportTaskService importTaskService;

    @Resource
    private SignatureVerificationService signatureVerificationService;

    @Resource
    private PasswordVerificationService passwordVerificationService;

    @PostMapping("/tasks")
    @Operation(summary = "创建导入任务")
    public CommonResult<CreateImportTaskRespVO> createImportTask(@Valid @RequestBody CreateImportTaskReqVO reqVO) {
        Long userId = SecurityFrameworkUtils.getLoginUserId();
        String taskId = importTaskService.createImportTask(userId, reqVO.getAccountBookId());
        
        ImportTaskDO task = importTaskService.getByTaskId(taskId);
        CreateImportTaskRespVO respVO = new CreateImportTaskRespVO();
        respVO.setTaskId(taskId);
        respVO.setUniqueEmail(task.getUniqueEmail());
        
        return success(respVO);
    }

    @PostMapping("/receive-raw-email")
    @Operation(summary = "接收原始邮件")
    public CommonResult<Boolean> receiveRawEmail(@Valid @RequestBody ReceiveRawEmailReqVO reqVO) {
        // 验证签名
        if (!signatureVerificationService.verifySignature(
                reqVO.getSignature(), reqVO.getTimestamp(), reqVO.getRawEmailData())) {
            log.warn("邮件签名验证失败");
            return success(false);
        }

        // 将原始邮件数据推入预处理队列
        importerProducer.sendMailPreprocessMessage(reqVO.getRawEmailData());
        
        return success(true);
    }

    @PostMapping("/tasks/{taskId}/submit-password")
    @Operation(summary = "提交解压密码")
    public CommonResult<Boolean> submitPassword(
            @Parameter(description = "任务ID") @PathVariable String taskId,
            @Valid @RequestBody SubmitPasswordReqVO reqVO) {
        
        // 获取任务信息
        ImportTaskDO task = importTaskService.getByTaskId(taskId);
        if (task == null) {
            log.warn("任务不存在: {}", taskId);
            return success(false);
        }

        if (!ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus().equals(task.getStatus())) {
            log.warn("任务状态不正确: taskId={}, status={}", taskId, task.getStatus());
            return success(false);
        }

        // 快速验证密码
        boolean isPasswordValid = passwordVerificationService.quickVerifyZipPassword(
                task.getFilePath(), reqVO.getPassword());
        
        if (!isPasswordValid) {
            throw exception(UNZIP_CODE_IS_WRONG);
        }

        // 密码验证成功，保存密码并推送到解析队列
        importTaskService.updateUnzipPassword(taskId, reqVO.getPassword());
        importerProducer.sendBillParsingMessage(taskId, task.getFilePath(), reqVO.getPassword());
        
        return success(true);
    }

}