package cn.iocoder.yudao.module.importer.mq.message;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessage;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 邮件上报消息
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MailReportMessage extends AbstractRedisStreamMessage {

    public static final String TOPIC = "mail-report";

    /**
     * 云端对象存储Key
     */
    private String objectKey;

    /**
     * 发件人邮箱
     */
    private String sender;

    /**
     * 收件人邮箱
     */
    private String recipient;

    /**
     * 邮件唯一ID
     */
    private String emailId;

    /**
     * 邮件接收时间
     */
    private LocalDateTime receivedTime;

    @Override
    public String getStreamKey() {
        return TOPIC;
    }

}
