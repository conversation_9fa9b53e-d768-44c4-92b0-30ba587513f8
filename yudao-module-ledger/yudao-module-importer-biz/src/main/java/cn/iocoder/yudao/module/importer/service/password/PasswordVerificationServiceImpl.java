package cn.iocoder.yudao.module.importer.service.password;

import cn.iocoder.yudao.module.infra.service.file.FileService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.ZipFile;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

/**
 * 密码验证服务实现类
 */
@Service
@Slf4j
public class PasswordVerificationServiceImpl implements PasswordVerificationService {

    @Resource
    private FileService fileService;

    @Override
    public boolean quickVerifyZipPassword(String filePath, String password) {
        Path tempFile = null;
        try {
            // 从OSS下载文件头部分（前几KB）
            byte[] fileHead = downloadFileHead(filePath);
            if (fileHead == null) {
                log.warn("无法下载文件头: {}", filePath);
                return false;
            }

            // 创建临时文件
            tempFile = Files.createTempFile("zip-verify-", ".zip");
            Files.write(tempFile, fileHead);

            // 使用zip4j验证密码
            try (ZipFile zipFile = new ZipFile(tempFile.toFile())) {
                if (zipFile.isEncrypted()) {
                    zipFile.setPassword(password.toCharArray());
                    // 尝试读取文件头信息，不解压
                    zipFile.getFileHeaders();
                    return true;
                } else {
                    // 文件未加密
                    return true;
                }
            }

        } catch (Exception e) {
            log.debug("密码验证失败: filePath={}, error={}", filePath, e.getMessage());
            return false;
        } finally {
            // 清理临时文件
            if (tempFile != null) {
                try {
                    Files.deleteIfExists(tempFile);
                } catch (IOException e) {
                    log.warn("删除临时文件失败: {}", tempFile, e);
                }
            }
        }
    }

    private byte[] downloadFileHead(String filePath) {
        try {
            // 这里假设fileService支持范围下载，如果不支持则需要下载完整文件
            // 实际实现中可能需要根据具体的文件服务调整
            Long configId = 24L; // 这里需要根据实际情况获取配置ID
            byte[] content = fileService.getFileContent(configId, filePath);

            // 检查文件是否为空
            if (content == null || content.length == 0) {
                log.warn("文件为空或不存在: {}", filePath);
                return null;
            }

            return content;
        } catch (Exception e) {
            log.error("下载文件头失败: {}", filePath, e);
            return null;
        }
    }

}