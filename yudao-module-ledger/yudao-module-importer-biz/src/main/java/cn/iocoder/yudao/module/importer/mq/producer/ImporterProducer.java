package cn.iocoder.yudao.module.importer.mq.producer;

import cn.iocoder.yudao.framework.mq.redis.core.RedisMQTemplate;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReceiveEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailReqVO;
import cn.iocoder.yudao.module.importer.mq.message.*;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class ImporterProducer {

    @Resource
    private RedisMQTemplate redisMQTemplate;

    /**
     * 发送 {@link ReceiveEmailMessage} 消息
     */
    public void sendReceiveEmailMessage(@Valid ReceiveEmailReqVO reqVO) {
        ReceiveEmailMessage msg = new ReceiveEmailMessage().setObjectKey(reqVO.getObjectKey())
                .setRecipient(reqVO.getRecipient());
        log.info("send ReceiveEmailMessage:{}]", reqVO);
        redisMQTemplate.send(msg);
    }

    /**
     * 发送邮件预处理消息
     */
    public void sendMailPreprocessMessage(String rawEmailData) {
        MailPreprocessMessage msg = new MailPreprocessMessage();
        msg.setRawEmailData(rawEmailData);
        log.info("send MailPreprocessMessage");
        redisMQTemplate.send(msg);
    }

    /**
     * 发送账单解析消息
     */
    public void sendBillParsingMessage(String taskId, String s3Path, String password) {
        BillParsingMessage msg = new BillParsingMessage();
        msg.setTaskId(taskId);
        msg.setS3Path(s3Path);
        msg.setPassword(password);
        log.info("send BillParsingMessage: taskId={}", taskId);
        redisMQTemplate.send(msg);
    }

    public void sendSubmitUnzipCodeMessage(String importTaskId) {
        log.info("send SubmitUnzipCodeMessage:{}]", importTaskId);
        redisMQTemplate.send(new SubmitUnzipCodeMessage().setImportTaskId(importTaskId));
    }

    /**
     * 发送邮件上报消息
     */
    public void sendMailReportMessage(@Valid ReportEmailReqVO reqVO) {
        MailReportMessage msg = new MailReportMessage();
        msg.setObjectKey(reqVO.getObjectKey());
        msg.setSender(reqVO.getSender());
        msg.setRecipient(reqVO.getRecipient());
        msg.setEmailId(reqVO.getEmailId());
        msg.setReceivedTime(reqVO.getReceivedTime());
        log.info("send MailReportMessage: objectKey={}, emailId={}", reqVO.getObjectKey(), reqVO.getEmailId());
        redisMQTemplate.send(msg);
    }

}