package cn.iocoder.yudao.module.importer.service.mailserver;

import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailRespVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ValidateEmailRespVO;

/**
 * 邮件服务器服务接口
 */
public interface MailServerService {

    /**
     * 校验邮件合法性
     *
     * @param sender 发件人邮箱
     * @param recipient 收件人邮箱
     * @return 校验结果
     */
    ValidateEmailRespVO validateEmail(String sender, String recipient);

    /**
     * 上报邮件
     *
     * @param reqVO 上报邮件请求
     * @return 上报结果
     */
    ReportEmailRespVO reportEmail(ReportEmailReqVO reqVO);

}
