### 接收邮件导入请求
POST {{appApi}}/ledger/import/receive-email
Content-Type: application/json

{
  "objectKey": "jing_wx_bill.eml",
  "recipient": "<EMAIL>"
}

###
POST {{appApi}}/ledger/import/receive-email
Content-Type: application/json

{
  "objectKey": "微信支付-账单流水文件20241012七天有效.eml",
  "recipient": "<EMAIL>"
}

###
POST {{appApi}}/ledger/import/receive-email
Content-Type: application/json

{
  "objectKey": "微信支付-账单流水文件202410241330.eml",
  "recipient": "<EMAIL>"
}

### 解压码正确
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "762493b10266c24f47238a0ef8f8dad0925e99309cf24cafadda62e5882a294c.zip",
  "unzipCode": "703534"
}

### 解压码错误
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "762493b10266c24f47238a0ef8f8dad0925e99309cf24cafadda62e5882a294c.zip",
  "unzipCode": "000000"
}

### zip文件不存在
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "not_exists.zip",
  "unzipCode": "703534"
}


### 文件错误
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "jing_wx_bill.eml",
  "unzipCode": "703534"
}

### 接收原始邮件 - 签名验证成功
POST {{appApi}}/ledger/import/receive-raw-email
Content-Type: application/json

{
  "signature": "请使用HMAC-SHA256算法计算: timestamp|rawEmailData",
  "timestamp": "1634567890000",
  "rawEmailData": "Received: from mail.example.com (mail.example.com [*************])\n\tby mx.example.com (Postfix) with ESMTP id 12345\n\tfor <<EMAIL>>; Mon, 18 Oct 2021 10:30:00 +0800 (CST)\nFrom: <EMAIL>\nTo: <EMAIL>\nSubject: 微信支付账单\nDate: Mon, 18 Oct 2021 10:30:00 +0800\nContent-Type: multipart/mixed; boundary=\"boundary123\"\n\n--boundary123\nContent-Type: text/plain\n\n您的微信支付账单已生成，请查收附件。\n\n--boundary123\nContent-Type: application/zip\nContent-Disposition: attachment; filename=\"wxpay_bill_20211018.zip\"\n\n[ZIP文件二进制数据]\n--boundary123--"
}

### 接收原始邮件 - 签名验证失败
POST {{appApi}}/ledger/import/receive-raw-email
Content-Type: application/json

{
  "signature": "invalid_signature_example",
  "timestamp": "1634567890000",
  "rawEmailData": "Received: from mail.example.com (mail.example.com [*************])\n\tby mx.example.com (Postfix) with ESMTP id 12345\n\tfor <<EMAIL>>; Mon, 18 Oct 2021 10:30:00 +0800 (CST)\nFrom: <EMAIL>\nTo: <EMAIL>\nSubject: 微信支付账单\nDate: Mon, 18 Oct 2021 10:30:00 +0800\nContent-Type: text/plain\n\n测试邮件内容"
}

### 接收原始邮件 - 支付宝账单邮件
POST {{appApi}}/ledger/import/receive-raw-email
Content-Type: application/json

{
  "signature": "请使用配置的密钥计算HMAC-SHA256签名",
  "timestamp": "1634567900000",
  "rawEmailData": "Received: from mail.alipay.com (mail.alipay.com [*************])\n\tby mx.example.com (Postfix) with ESMTP id 67890\n\tfor <<EMAIL>>; Mon, 18 Oct 2021 11:00:00 +0800 (CST)\nFrom: <EMAIL>\nTo: <EMAIL>\nSubject: 支付宝账单\nDate: Mon, 18 Oct 2021 11:00:00 +0800\nContent-Type: multipart/mixed; boundary=\"alipay_boundary\"\n\n--alipay_boundary\nContent-Type: text/html\n\n<html><body>您的支付宝账单已生成</body></html>\n\n--alipay_boundary\nContent-Type: application/zip\nContent-Disposition: attachment; filename=\"alipay_bill_20211018.zip\"\n\n[ZIP文件二进制数据]\n--alipay_boundary--"
}

### 提交解压密码 - 密码正确
POST {{appApi}}/ledger/import/tasks/abc123def456/submit-password
Content-Type: application/json

{
  "password": "123456"
}

### 提交解压密码 - 密码错误
POST {{appApi}}/ledger/import/tasks/abc123def456/submit-password
Content-Type: application/json

{
  "password": "wrong_password"
}

### 提交解压密码 - 任务不存在
POST {{appApi}}/ledger/import/tasks/nonexistent_task_id/submit-password
Content-Type: application/json

{
  "password": "123456"
}

### 提交解压密码 - 微信支付账单
POST {{appApi}}/ledger/import/tasks/wx_task_789/submit-password
Content-Type: application/json

{
  "password": "703534"
}

### 提交解压密码 - 支付宝账单
POST {{appApi}}/ledger/import/tasks/alipay_task_456/submit-password
Content-Type: application/json

{
  "password": "888888"
}

### ==================== 签名计算示例 ====================
###
### 如何计算正确的签名：
### 1. 载荷格式：timestamp|rawEmailData
### 2. 算法：HMAC-SHA256
### 3. 密钥：配置的 mail-server.secret-key
### 4. 输出：十六进制字符串
###
### Java 示例代码：
### ```java
### String timestamp = "1634567890000";
### String rawEmailData = "your-email-data";
### String secretKey = "your-configured-secret-key";
### String payload = timestamp + "|" + rawEmailData;
### HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
### String signature = HexUtil.encodeHexStr(hMac.digest(payload));
### ```
###
### Python 示例代码：
### ```python
### import hmac
### import hashlib
###
### timestamp = "1634567890000"
### raw_email_data = "your-email-data"
### secret_key = "your-configured-secret-key"
### payload = f"{timestamp}|{raw_email_data}"
### signature = hmac.new(
###     secret_key.encode('utf-8'),
###     payload.encode('utf-8'),
###     hashlib.sha256
### ).hexdigest()
### ```
###
### 注意事项：
### 1. 时间戳必须是毫秒级（13位数字）
### 2. 时间戳不能超过5分钟（可配置）
### 3. 密钥必须至少32位且不能包含弱模式
### 4. 所有字符串使用UTF-8编码