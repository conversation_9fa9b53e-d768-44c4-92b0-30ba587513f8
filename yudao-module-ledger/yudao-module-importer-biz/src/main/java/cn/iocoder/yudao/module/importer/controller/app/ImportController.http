### 接收邮件导入请求
POST {{appApi}}/ledger/import/receive-email
Content-Type: application/json

{
  "objectKey": "jing_wx_bill.eml",
  "recipient": "<EMAIL>"
}

###
POST {{appApi}}/ledger/import/receive-email
Content-Type: application/json

{
  "objectKey": "微信支付-账单流水文件20241012七天有效.eml",
  "recipient": "<EMAIL>"
}

###
POST {{appApi}}/ledger/import/receive-email
Content-Type: application/json

{
  "objectKey": "微信支付-账单流水文件202410241330.eml",
  "recipient": "<EMAIL>"
}

### 解压码正确
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "762493b10266c24f47238a0ef8f8dad0925e99309cf24cafadda62e5882a294c.zip",
  "unzipCode": "703534"
}

### 解压码错误
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "762493b10266c24f47238a0ef8f8dad0925e99309cf24cafadda62e5882a294c.zip",
  "unzipCode": "000000"
}

### zip文件不存在
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "not_exists.zip",
  "unzipCode": "703534"
}


### 文件错误
POST {{appApi}}/ledger/import/submit-unzip-code
Content-Type: application/json

{
  "importTaskId": "jing_wx_bill.eml",
  "unzipCode": "703534"
}



### 提交解压密码 - 密码正确
POST {{appApi}}/ledger/import/tasks/abc123def456/submit-password
Content-Type: application/json

{
  "password": "123456"
}

### 提交解压密码 - 密码错误
POST {{appApi}}/ledger/import/tasks/abc123def456/submit-password
Content-Type: application/json

{
  "password": "wrong_password"
}

### 提交解压密码 - 任务不存在
POST {{appApi}}/ledger/import/tasks/nonexistent_task_id/submit-password
Content-Type: application/json

{
  "password": "123456"
}

### 提交解压密码 - 微信支付账单
POST {{appApi}}/ledger/import/tasks/wx_task_789/submit-password
Content-Type: application/json

{
  "password": "703534"
}

### 提交解压密码 - 支付宝账单
POST {{appApi}}/ledger/import/tasks/alipay_task_456/submit-password
Content-Type: application/json

{
  "password": "888888"
}

### ==================== 签名计算示例 ====================
###
### 如何计算正确的签名：
### 1. 载荷格式：timestamp|rawEmailData
### 2. 算法：HMAC-SHA256
### 3. 密钥：配置的 mail-server.secret-key
### 4. 输出：十六进制字符串
###
### Java 示例代码：
### ```java
### String timestamp = "1634567890000";
### String rawEmailData = "your-email-data";
### String secretKey = "your-configured-secret-key";
### String payload = timestamp + "|" + rawEmailData;
### HMac hMac = new HMac(HmacAlgorithm.HmacSHA256, secretKey.getBytes(StandardCharsets.UTF_8));
### String signature = HexUtil.encodeHexStr(hMac.digest(payload));
### ```
###
### Python 示例代码：
### ```python
### import hmac
### import hashlib
###
### timestamp = "1634567890000"
### raw_email_data = "your-email-data"
### secret_key = "your-configured-secret-key"
### payload = f"{timestamp}|{raw_email_data}"
### signature = hmac.new(
###     secret_key.encode('utf-8'),
###     payload.encode('utf-8'),
###     hashlib.sha256
### ).hexdigest()
### ```
###
### 注意事项：
### 1. 时间戳必须是毫秒级（13位数字）
### 2. 时间戳不能超过5分钟（可配置）
### 3. 密钥必须至少32位且不能包含弱模式
### 4. 所有字符串使用UTF-8编码