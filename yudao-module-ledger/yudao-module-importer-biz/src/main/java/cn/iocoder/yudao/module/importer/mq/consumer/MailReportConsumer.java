package cn.iocoder.yudao.module.importer.mq.consumer;

import cn.iocoder.yudao.framework.mq.redis.core.stream.AbstractRedisStreamMessageListener;
import cn.iocoder.yudao.module.importer.mq.message.MailReportMessage;
import cn.iocoder.yudao.module.importer.service.email.EmailProcessingService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 邮件上报消息消费者
 */
@Slf4j
@Component
public class MailReportConsumer extends AbstractRedisStreamMessageListener<MailReportMessage> {

    @Resource
    private EmailProcessingService emailProcessingService;

    @Override
    public void onMessage(MailReportMessage message) {
        log.info("[MailReportMessage][收到邮件上报消息] objectKey={}, emailId={}", 
                message.getObjectKey(), message.getEmailId());

        try {
            emailProcessingService.processReportedEmail(message);
            log.info("[MailReportMessage][邮件上报处理完成] objectKey={}, emailId={}", 
                    message.getObjectKey(), message.getEmailId());
        } catch (Exception e) {
            log.error("[MailReportMessage][邮件上报处理失败] objectKey={}, emailId={}", 
                    message.getObjectKey(), message.getEmailId(), e);
            throw e;
        }
    }
}
