package cn.iocoder.yudao.module.importer.controller.app.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.time.LocalDateTime;

@Schema(description = "邮件服务器 - 上报邮件 Request VO")
@Data
public class ReportEmailReqVO {

    @Schema(description = "邮件服务器签名", requiredMode = Schema.RequiredMode.REQUIRED, example = "abc123def456")
    @NotBlank(message = "签名不能为空")
    private String signature;

    @Schema(description = "时间戳", requiredMode = Schema.RequiredMode.REQUIRED, example = "1634567890000")
    @NotBlank(message = "时间戳不能为空")
    private String timestamp;

    @Schema(description = "云端对象存储Key", requiredMode = Schema.RequiredMode.REQUIRED, example = "emails/2024/01/02/email-123.eml")
    @NotBlank(message = "对象存储Key不能为空")
    private String objectKey;

    @Schema(description = "发件人邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    @NotBlank(message = "发件人邮箱不能为空")
    @Email(message = "发件人邮箱格式不正确")
    private String sender;

    @Schema(description = "收件人邮箱", requiredMode = Schema.RequiredMode.REQUIRED, example = "<EMAIL>")
    @NotBlank(message = "收件人邮箱不能为空")
    @Email(message = "收件人邮箱格式不正确")
    private String recipient;

    @Schema(description = "邮件唯一ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "email-uuid-123456")
    @NotBlank(message = "邮件唯一ID不能为空")
    private String emailId;

    @Schema(description = "邮件接收时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024-01-02T10:30:00")
    @NotNull(message = "邮件接收时间不能为空")
    private LocalDateTime receivedTime;

}
