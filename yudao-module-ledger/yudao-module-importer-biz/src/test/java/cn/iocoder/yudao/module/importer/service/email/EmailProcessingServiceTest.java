package cn.iocoder.yudao.module.importer.service.email;

import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link EmailProcessingServiceImpl} 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class EmailProcessingServiceTest {

    @Mock
    private ImportTaskService importTaskService;

    @Mock
    private FileService fileService;

    @InjectMocks
    private EmailProcessingServiceImpl emailProcessingService;

    private String createTestEmailData(String recipientEmail, String senderEmail) {
        return "Return-Path: <" + senderEmail + ">\r\n" +
               "Received: from mail.example.com\r\n" +
               "Date: Mon, 01 Jul 2025 10:00:00 +0800\r\n" +
               "From: " + senderEmail + "\r\n" +
               "To: " + recipientEmail + "\r\n" +
               "Subject: =?UTF-8?B?6LSm5Y2V6YKu5Lu2?=\r\n" +  // 账单邮件 in Base64
               "MIME-Version: 1.0\r\n" +
               "Content-Type: multipart/mixed; boundary=\"boundary123\"\r\n" +
               "\r\n" +
               "--boundary123\r\n" +
               "Content-Type: text/plain; charset=UTF-8\r\n" +
               "Content-Transfer-Encoding: 8bit\r\n" +
               "\r\n" +
               "这是一封账单邮件\r\n" +
               "\r\n" +
               "--boundary123\r\n" +
               "Content-Type: application/zip\r\n" +
               "Content-Disposition: attachment; filename=\"bill.zip\"\r\n" +
               "Content-Transfer-Encoding: base64\r\n" +
               "\r\n" +
               "UEsDBAoAAAAAAKxQUVMAAAAAAAAAAAAAAAAJAAAAYmlsbC5jc3ZQSwECFAAKAAAAAACs\r\n" +
               "UFFTAAAAAAAAAAAAAAAACQAAAAAAAAAAAAAAAAAAAGJpbGwuY3N2UEsFBgAAAAAB\r\n" +
               "AAEANwAAAB8AAAAAAA==\r\n" +  // 简单的ZIP文件Base64编码
               "--boundary123--\r\n";
    }

    @Test
    public void testProcessRawEmail_Success() {
        // 准备测试数据
        String taskId = "test-task-123";
        String recipientEmail = "import-" + taskId + "@import.ledger.com";
        String senderEmail = "<EMAIL>";
        String rawEmailData = createTestEmailData(recipientEmail, senderEmail);

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());
        task.setUserId(1L);
        task.setAccountBookId(100L);

        // Mock服务调用
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.createFile(anyString(), anyString(), any(byte[].class)))
                .thenReturn("http://oss.example.com/files/test.zip");

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatusAndFile(
                eq(taskId),
                eq(ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus()),
                eq("http://oss.example.com/files/test.zip"),
                anyString()
        );
    }

    @Test
    public void testProcessRawEmail_InvalidRecipientEmail() {
        // 准备测试数据 - 无效的收件人邮箱
        String rawEmailData = createTestEmailData("<EMAIL>", "<EMAIL>");

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证没有调用任务服务
        verify(importTaskService, never()).getByTaskId(anyString());
    }

    @Test
    public void testProcessRawEmail_TaskNotFound() {
        // 准备测试数据
        String taskId = "nonexistent-task";
        String recipientEmail = "import-" + taskId + "@import.ledger.com";
        String rawEmailData = createTestEmailData(recipientEmail, "<EMAIL>");

        // Mock服务调用 - 任务不存在
        when(importTaskService.getByTaskId(taskId)).thenReturn(null);

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService, never()).updateTaskStatusAndFile(anyString(), anyInt(), anyString(), anyString());
    }

    @Test
    public void testProcessRawEmail_WrongTaskStatus() {
        // 准备测试数据
        String taskId = "test-task-123";
        String recipientEmail = "import-" + taskId + "@import.ledger.com";
        String rawEmailData = createTestEmailData(recipientEmail, "<EMAIL>");

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatusEnum.COMPLETED.getStatus()); // 错误的状态

        // Mock服务调用
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService, never()).updateTaskStatusAndFile(anyString(), anyInt(), anyString(), anyString());
    }

    @Test
    public void testProcessRawEmail_UnknownEmailType() {
        // 准备测试数据 - 未知的发件人
        String taskId = "test-task-123";
        String recipientEmail = "import-" + taskId + "@import.ledger.com";
        String senderEmail = "<EMAIL>";
        String rawEmailData = createTestEmailData(recipientEmail, senderEmail);

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());

        // Mock服务调用
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskCompleted(
                eq(taskId),
                eq(ImportTaskStatusEnum.FAILED.getStatus()),
                eq(0),
                eq("无法识别邮件类型")
        );
    }

    @Test
    public void testProcessRawEmail_NoAttachment() {
        // 准备测试数据 - 没有附件的邮件
        String taskId = "test-task-123";
        String recipientEmail = "import-" + taskId + "@import.ledger.com";
        String senderEmail = "<EMAIL>";
        String rawEmailData = "From: " + senderEmail + "\r\n" +
                              "To: " + recipientEmail + "\r\n" +
                              "Subject: 测试邮件\r\n" +
                              "\r\n" +
                              "这是一封没有附件的邮件\r\n";

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());

        // Mock服务调用
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskCompleted(
                eq(taskId),
                eq(ImportTaskStatusEnum.FAILED.getStatus()),
                eq(0),
                eq("未找到邮件附件")
        );
    }

    @Test
    public void testProcessRawEmail_FileUploadFailure() {
        // 准备测试数据
        String taskId = "test-task-123";
        String recipientEmail = "import-" + taskId + "@import.ledger.com";
        String senderEmail = "<EMAIL>";
        String rawEmailData = createTestEmailData(recipientEmail, senderEmail);

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());

        // Mock服务调用 - 文件上传失败
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.createFile(anyString(), anyString(), any(byte[].class)))
                .thenThrow(new RuntimeException("文件上传失败"));

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        // 由于异常被捕获，不会更新任务状态
        verify(importTaskService, never()).updateTaskStatusAndFile(anyString(), anyInt(), anyString(), anyString());
    }

    @Test
    public void testProcessRawEmail_WeixinEmail() {
        // 准备测试数据 - 微信邮件
        String taskId = "test-task-456";
        String recipientEmail = "import-" + taskId + "@import.ledger.com";
        String senderEmail = "<EMAIL>";
        String rawEmailData = createTestEmailData(recipientEmail, senderEmail);

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId(taskId);
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());

        // Mock服务调用
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.createFile(anyString(), anyString(), any(byte[].class)))
                .thenReturn("http://oss.example.com/files/weixin.zip");

        // 执行测试
        emailProcessingService.processRawEmail(rawEmailData);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatusAndFile(
                eq(taskId),
                eq(ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus()),
                eq("http://oss.example.com/files/weixin.zip"),
                anyString()
        );
    }
}
