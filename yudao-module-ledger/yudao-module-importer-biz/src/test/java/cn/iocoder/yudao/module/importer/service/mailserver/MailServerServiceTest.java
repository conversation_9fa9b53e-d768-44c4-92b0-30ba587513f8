package cn.iocoder.yudao.module.importer.service.mailserver;

import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ReportEmailRespVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ValidateEmailRespVO;
import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.mq.producer.ImporterProducer;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * {@link MailServerServiceImpl} 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class MailServerServiceTest {

    @Mock
    private ImportTaskService importTaskService;

    @Mock
    private ImporterProducer importerProducer;

    @InjectMocks
    private MailServerServiceImpl mailServerService;

    @Test
    public void testValidateEmail_Success() {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId("test123");
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());
        task.setUniqueEmail(recipient);

        // Mock服务调用
        when(importTaskService.getByUniqueEmail(recipient)).thenReturn(task);

        // 执行测试
        ValidateEmailRespVO result = mailServerService.validateEmail(sender, recipient);

        // 验证结果
        assertTrue(result.getValid());
        assertNull(result.getErrorMessage());
        verify(importTaskService).getByUniqueEmail(recipient);
    }

    @Test
    public void testValidateEmail_InvalidRecipientFormat() {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";

        // 执行测试
        ValidateEmailRespVO result = mailServerService.validateEmail(sender, recipient);

        // 验证结果
        assertFalse(result.getValid());
        assertEquals("收件人邮箱格式不正确，不是有效的导入任务邮箱", result.getErrorMessage());
        verify(importTaskService, never()).getByUniqueEmail(any());
    }

    @Test
    public void testValidateEmail_TaskNotFound() {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";

        // Mock服务调用 - 任务不存在
        when(importTaskService.getByUniqueEmail(recipient)).thenReturn(null);

        // 执行测试
        ValidateEmailRespVO result = mailServerService.validateEmail(sender, recipient);

        // 验证结果
        assertFalse(result.getValid());
        assertEquals("导入任务不存在", result.getErrorMessage());
        verify(importTaskService).getByUniqueEmail(recipient);
    }

    @Test
    public void testValidateEmail_WrongTaskStatus() {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId("test123");
        task.setStatus(ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus()); // 错误状态
        task.setUniqueEmail(recipient);

        // Mock服务调用
        when(importTaskService.getByUniqueEmail(recipient)).thenReturn(task);

        // 执行测试
        ValidateEmailRespVO result = mailServerService.validateEmail(sender, recipient);

        // 验证结果
        assertFalse(result.getValid());
        assertTrue(result.getErrorMessage().contains("任务状态不正确"));
        verify(importTaskService).getByUniqueEmail(recipient);
    }

    @Test
    public void testValidateEmail_UnsupportedSender() {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId("test123");
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());
        task.setUniqueEmail(recipient);

        // Mock服务调用
        when(importTaskService.getByUniqueEmail(recipient)).thenReturn(task);

        // 执行测试
        ValidateEmailRespVO result = mailServerService.validateEmail(sender, recipient);

        // 验证结果
        assertFalse(result.getValid());
        assertEquals("不支持的发件人邮箱：" + sender, result.getErrorMessage());
        verify(importTaskService).getByUniqueEmail(recipient);
    }

    @Test
    public void testReportEmail_Success() {
        // 准备测试数据
        ReportEmailReqVO reqVO = new ReportEmailReqVO();
        reqVO.setObjectKey("emails/test123/email.eml");
        reqVO.setSender("<EMAIL>");
        reqVO.setRecipient("<EMAIL>");
        reqVO.setEmailId("email-123");
        reqVO.setReceivedTime(LocalDateTime.now());

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId("test123");
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());
        task.setUniqueEmail(reqVO.getRecipient());

        // Mock服务调用
        when(importTaskService.getByUniqueEmail(reqVO.getRecipient())).thenReturn(task);

        // 执行测试
        ReportEmailRespVO result = mailServerService.reportEmail(reqVO);

        // 验证结果
        assertTrue(result.getSuccess());
        assertNull(result.getErrorMessage());
        verify(importerProducer).sendMailReportMessage(reqVO);
    }

    @Test
    public void testReportEmail_ValidationFailure() {
        // 准备测试数据
        ReportEmailReqVO reqVO = new ReportEmailReqVO();
        reqVO.setObjectKey("emails/test123/email.eml");
        reqVO.setSender("<EMAIL>");
        reqVO.setRecipient("<EMAIL>");
        reqVO.setEmailId("email-123");
        reqVO.setReceivedTime(LocalDateTime.now());

        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId("test123");
        task.setStatus(ImportTaskStatusEnum.AWAITING_EMAIL.getStatus());
        task.setUniqueEmail(reqVO.getRecipient());

        // Mock服务调用
        when(importTaskService.getByUniqueEmail(reqVO.getRecipient())).thenReturn(task);

        // 执行测试
        ReportEmailRespVO result = mailServerService.reportEmail(reqVO);

        // 验证结果
        assertFalse(result.getSuccess());
        assertTrue(result.getErrorMessage().contains("邮件校验失败"));
        verify(importerProducer, never()).sendMailReportMessage(any());
    }

}
