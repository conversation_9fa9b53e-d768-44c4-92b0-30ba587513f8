package cn.iocoder.yudao.module.importer.service.password;

import cn.iocoder.yudao.module.infra.service.file.FileService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

/**
 * {@link PasswordVerificationServiceImpl} 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class PasswordVerificationServiceTest {

    @Mock
    private FileService fileService;

    @InjectMocks
    private PasswordVerificationServiceImpl passwordVerificationService;

    private byte[] createTestZipFile(String password, boolean encrypted) throws IOException {
        Path tempFile = Files.createTempFile("test", ".zip");
        try {
            try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(tempFile))) {
                if (encrypted) {
                    // 注意：这里只是模拟，实际的加密ZIP需要使用zip4j等库
                    // 在真实测试中，应该使用zip4j创建加密的ZIP文件
                }
                
                ZipEntry entry = new ZipEntry("test.csv");
                zos.putNextEntry(entry);
                zos.write("test,data\n1,2".getBytes());
                zos.closeEntry();
            }
            return Files.readAllBytes(tempFile);
        } finally {
            Files.deleteIfExists(tempFile);
        }
    }

    @Test
    public void testQuickVerifyZipPassword_Success() throws Exception {
        // 准备测试数据
        String filePath = "test/path/file.zip";
        String password = "123456";
        byte[] zipContent = createTestZipFile(password, false); // 未加密的ZIP用于测试

        // Mock文件服务
        when(fileService.getFileContent(eq(24L), eq(filePath))).thenReturn(zipContent);

        // 执行测试
        boolean result = passwordVerificationService.quickVerifyZipPassword(filePath, password);

        // 验证结果 - 未加密的ZIP应该返回true
        assertTrue(result);
    }

    @Test
    public void testQuickVerifyZipPassword_FileNotFound() throws Exception {
        // 准备测试数据
        String filePath = "test/path/nonexistent.zip";
        String password = "123456";

        // Mock文件服务返回null
        when(fileService.getFileContent(eq(24L), eq(filePath))).thenReturn(null);

        // 执行测试
        boolean result = passwordVerificationService.quickVerifyZipPassword(filePath, password);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testQuickVerifyZipPassword_FileServiceException() throws Exception {
        // 准备测试数据
        String filePath = "test/path/file.zip";
        String password = "123456";

        // Mock文件服务抛出异常
        when(fileService.getFileContent(eq(24L), eq(filePath)))
                .thenThrow(new RuntimeException("File service error"));

        // 执行测试
        boolean result = passwordVerificationService.quickVerifyZipPassword(filePath, password);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testQuickVerifyZipPassword_InvalidZipFile() throws Exception {
        // 准备测试数据
        String filePath = "test/path/invalid.zip";
        String password = "123456";
        byte[] invalidContent = "not a zip file".getBytes();

        // Mock文件服务
        when(fileService.getFileContent(eq(24L), eq(filePath))).thenReturn(invalidContent);

        // 执行测试
        boolean result = passwordVerificationService.quickVerifyZipPassword(filePath, password);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testQuickVerifyZipPassword_EmptyFile() throws Exception {
        // 准备测试数据
        String filePath = "test/path/empty.zip";
        String password = "123456";
        byte[] emptyContent = new byte[0];

        // Mock文件服务
        when(fileService.getFileContent(eq(24L), eq(filePath))).thenReturn(emptyContent);

        // 执行测试
        boolean result = passwordVerificationService.quickVerifyZipPassword(filePath, password);

        // 验证结果
        assertFalse(result);
    }

    @Test
    public void testQuickVerifyZipPassword_NullPassword() throws Exception {
        // 准备测试数据
        String filePath = "test/path/file.zip";
        String password = null;
        byte[] zipContent = createTestZipFile("123456", false);

        // Mock文件服务
        when(fileService.getFileContent(eq(24L), eq(filePath))).thenReturn(zipContent);

        // 执行测试
        boolean result = passwordVerificationService.quickVerifyZipPassword(filePath, password);

        // 验证结果 - null密码应该能处理未加密的ZIP
        assertTrue(result);
    }

    @Test
    public void testQuickVerifyZipPassword_EmptyPassword() throws Exception {
        // 准备测试数据
        String filePath = "test/path/file.zip";
        String password = "";
        byte[] zipContent = createTestZipFile("", false);

        // Mock文件服务
        when(fileService.getFileContent(eq(24L), eq(filePath))).thenReturn(zipContent);

        // 执行测试
        boolean result = passwordVerificationService.quickVerifyZipPassword(filePath, password);

        // 验证结果
        assertTrue(result);
    }
}
