package cn.iocoder.yudao.module.importer.service.billing;

import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.service.BillParsingService;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import cn.iocoder.yudao.module.ledger.api.accountbook.AccountBookItemApi;
import cn.iocoder.yudao.module.ledger.api.accountbook.dto.AccountBookItemCreateReqDTO;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link BillProcessingServiceImpl} 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class BillProcessingServiceTest {

    @Mock
    private ImportTaskService importTaskService;

    @Mock
    private FileService fileService;

    @Mock
    private BillParsingService billParsingService;

    @Mock
    private AccountBookItemApi accountBookItemApi;

    @InjectMocks
    private BillProcessingServiceImpl billProcessingService;

    private ImportTaskDO createTestTask() {
        ImportTaskDO task = new ImportTaskDO();
        task.setTaskId("test-task-123");
        task.setUserId(1L);
        task.setAccountBookId(100L);
        task.setFileName("test-bill.zip");
        task.setFilePath("test/path/bill.zip");
        task.setStatus(ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus());
        return task;
    }

    private byte[] createTestZipFile() throws IOException {
        Path tempFile = Files.createTempFile("test", ".zip");
        try {
            try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(tempFile))) {
                // 添加CSV文件
                ZipEntry entry = new ZipEntry("alipay_record.csv");
                zos.putNextEntry(entry);
                zos.write("交易号,商家订单号,交易创建时间,付款时间,最近修改时间,交易来源地,类型,交易对方,商品名称,金额（元）,收/支,交易状态,服务费（元）,成功退款（元）,备注,资金状态\n".getBytes());
                zos.write("2023123112345678901,ORDER001,2023-12-31 10:00:00,2023-12-31 10:00:01,2023-12-31 10:00:01,其他,即时到账交易,测试商家,测试商品,100.00,支出,交易成功,0.00,0.00,,已收入\n".getBytes());
                zos.closeEntry();
            }
            return Files.readAllBytes(tempFile);
        } finally {
            Files.deleteIfExists(tempFile);
        }
    }

    private List<AccountBookItemCreateReqDTO> createTestAccountBookItems() {
        AccountBookItemCreateReqDTO item1 = new AccountBookItemCreateReqDTO();
        item1.setAccountBookId("100");
        item1.setUserId(1L);
        item1.setAmount(new BigDecimal("100.00"));
        item1.setRemark("测试商品");
        item1.setOccurredTime(LocalDateTime.now());

        AccountBookItemCreateReqDTO item2 = new AccountBookItemCreateReqDTO();
        item2.setAccountBookId("100");
        item2.setUserId(1L);
        item2.setAmount(new BigDecimal("50.00"));
        item2.setRemark("测试商品2");
        item2.setOccurredTime(LocalDateTime.now());

        return Arrays.asList(item1, item2);
    }

    @Test
    public void testProcessBill_Success() throws Exception {
        // 准备测试数据
        String taskId = "test-task-123";
        String s3Path = "test/path/bill.zip";
        String password = "123456";
        ImportTaskDO task = createTestTask();
        byte[] zipContent = createTestZipFile();
        List<AccountBookItemCreateReqDTO> accountBookItems = createTestAccountBookItems();

        // Mock服务调用
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.getFileContent(eq(24L), eq(s3Path))).thenReturn(zipContent);
        when(billParsingService.parseBillFile(any(), eq(100L), eq(1L))).thenReturn(accountBookItems);
        when(accountBookItemApi.createAccountBookItem(any())).thenReturn(1L, 2L);

        // 执行测试
        billProcessingService.processBill(taskId, s3Path, password);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatus(taskId, ImportTaskStatusEnum.PROCESSING.getStatus());
        verify(fileService).getFileContent(24L, s3Path);
        verify(billParsingService).parseBillFile(any(), eq(100L), eq(1L));
        verify(accountBookItemApi, times(2)).createAccountBookItem(any());
        verify(importTaskService).updateTaskCompleted(taskId, ImportTaskStatusEnum.COMPLETED.getStatus(), 2, null);
    }

    @Test
    public void testProcessBill_TaskNotFound() throws Exception {
        // 准备测试数据
        String taskId = "nonexistent-task";
        String s3Path = "test/path/bill.zip";
        String password = "123456";

        // Mock服务调用 - 任务不存在
        when(importTaskService.getByTaskId(taskId)).thenReturn(null);

        // 执行测试
        billProcessingService.processBill(taskId, s3Path, password);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService, never()).updateTaskStatus(anyString(), anyInt());
        verify(fileService, never()).getFileContent(anyLong(), anyString());
    }

    @Test
    public void testProcessBill_FileDownloadFailure() throws Exception {
        // 准备测试数据
        String taskId = "test-task-123";
        String s3Path = "test/path/bill.zip";
        String password = "123456";
        ImportTaskDO task = createTestTask();

        // Mock服务调用 - 文件下载失败
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.getFileContent(eq(24L), eq(s3Path))).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            billProcessingService.processBill(taskId, s3Path, password);
        });

        // 验证异常信息
        assertEquals("账单处理失败", exception.getMessage());
        assertTrue(exception.getCause().getMessage().contains("下载文件失败"));

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatus(taskId, ImportTaskStatusEnum.PROCESSING.getStatus());
        verify(fileService).getFileContent(24L, s3Path);
        verify(importTaskService).updateTaskCompleted(
                eq(taskId),
                eq(ImportTaskStatusEnum.FAILED.getStatus()),
                eq(0),
                contains("下载文件失败")
        );
    }

    @Test
    public void testProcessBill_EmptyFile() throws Exception {
        // 准备测试数据
        String taskId = "test-task-123";
        String s3Path = "test/path/bill.zip";
        String password = "123456";
        ImportTaskDO task = createTestTask();
        byte[] emptyContent = new byte[0];

        // Mock服务调用 - 空文件
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.getFileContent(eq(24L), eq(s3Path))).thenReturn(emptyContent);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            billProcessingService.processBill(taskId, s3Path, password);
        });

        // 验证异常信息
        assertEquals("账单处理失败", exception.getMessage());
        assertTrue(exception.getCause().getMessage().contains("下载文件失败"));

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatus(taskId, ImportTaskStatusEnum.PROCESSING.getStatus());
        verify(fileService).getFileContent(24L, s3Path);
        verify(importTaskService).updateTaskCompleted(
                eq(taskId),
                eq(ImportTaskStatusEnum.FAILED.getStatus()),
                eq(0),
                contains("下载文件失败")
        );
    }

    @Test
    public void testProcessBill_InvalidZipFile() throws Exception {
        // 准备测试数据
        String taskId = "test-task-123";
        String s3Path = "test/path/bill.zip";
        String password = "123456";
        ImportTaskDO task = createTestTask();
        byte[] invalidContent = "not a zip file".getBytes();

        // Mock服务调用 - 无效的ZIP文件
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.getFileContent(eq(24L), eq(s3Path))).thenReturn(invalidContent);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            billProcessingService.processBill(taskId, s3Path, password);
        });

        // 验证异常信息
        assertEquals("账单处理失败", exception.getMessage());

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatus(taskId, ImportTaskStatusEnum.PROCESSING.getStatus());
        verify(fileService).getFileContent(24L, s3Path);
        verify(importTaskService).updateTaskCompleted(
                eq(taskId),
                eq(ImportTaskStatusEnum.FAILED.getStatus()),
                eq(0),
                anyString()
        );
    }

    @Test
    public void testProcessBill_NoCSVFiles() throws Exception {
        // 准备测试数据
        String taskId = "test-task-123";
        String s3Path = "test/path/bill.zip";
        String password = "123456";
        ImportTaskDO task = createTestTask();

        // 创建不包含CSV文件的ZIP
        Path tempFile = Files.createTempFile("test", ".zip");
        byte[] zipContent;
        try {
            try (ZipOutputStream zos = new ZipOutputStream(Files.newOutputStream(tempFile))) {
                ZipEntry entry = new ZipEntry("readme.txt");
                zos.putNextEntry(entry);
                zos.write("This is a readme file".getBytes());
                zos.closeEntry();
            }
            zipContent = Files.readAllBytes(tempFile);
        } finally {
            Files.deleteIfExists(tempFile);
        }

        // Mock服务调用
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.getFileContent(eq(24L), eq(s3Path))).thenReturn(zipContent);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            billProcessingService.processBill(taskId, s3Path, password);
        });

        // 验证异常信息
        assertEquals("账单处理失败", exception.getMessage());
        assertTrue(exception.getCause().getMessage().contains("未找到CSV文件"));

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatus(taskId, ImportTaskStatusEnum.PROCESSING.getStatus());
        verify(fileService).getFileContent(24L, s3Path);
        verify(importTaskService).updateTaskCompleted(
                eq(taskId),
                eq(ImportTaskStatusEnum.FAILED.getStatus()),
                eq(0),
                contains("未找到CSV文件")
        );
    }

    @Test
    public void testProcessBill_PartialSuccess() throws Exception {
        // 准备测试数据
        String taskId = "test-task-123";
        String s3Path = "test/path/bill.zip";
        String password = "123456";
        ImportTaskDO task = createTestTask();
        byte[] zipContent = createTestZipFile();
        List<AccountBookItemCreateReqDTO> accountBookItems = createTestAccountBookItems();

        // Mock服务调用 - 部分成功
        when(importTaskService.getByTaskId(taskId)).thenReturn(task);
        when(fileService.getFileContent(eq(24L), eq(s3Path))).thenReturn(zipContent);
        when(billParsingService.parseBillFile(any(), eq(100L), eq(1L))).thenReturn(accountBookItems);
        when(accountBookItemApi.createAccountBookItem(any()))
                .thenReturn(1L) // 第一个成功
                .thenThrow(new RuntimeException("创建失败")); // 第二个失败

        // 执行测试
        billProcessingService.processBill(taskId, s3Path, password);

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(importTaskService).updateTaskStatus(taskId, ImportTaskStatusEnum.PROCESSING.getStatus());
        verify(fileService).getFileContent(24L, s3Path);
        verify(billParsingService).parseBillFile(any(), eq(100L), eq(1L));
        verify(accountBookItemApi, times(2)).createAccountBookItem(any());
        verify(importTaskService).updateTaskCompleted(taskId, ImportTaskStatusEnum.COMPLETED.getStatus(), 1, null);
    }
}
