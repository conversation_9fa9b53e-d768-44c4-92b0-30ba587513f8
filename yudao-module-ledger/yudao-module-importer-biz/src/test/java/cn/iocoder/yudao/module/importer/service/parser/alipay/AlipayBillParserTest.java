package cn.iocoder.yudao.module.importer.service.parser.alipay;

import cn.iocoder.yudao.framework.test.core.ut.BaseMockitoUnitTest;
import cn.iocoder.yudao.module.importer.dal.dataobject.billfile.BillFileInfoDO;
import cn.iocoder.yudao.module.ledger.api.accountbook.dto.AccountBookItemCreateReqDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.io.File;
import java.net.URL;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * AlipayBillParser 单元测试
 *
 * <AUTHOR>
 */
public class AlipayBillParserTest extends BaseMockitoUnitTest {

    @InjectMocks
    private AlipayBillParser parser;

    @Mock
    private AlipayBillFileInfoParser fileInfoParser;

    @Mock
    private AlipayBillDetailParser detailParser;

    @Mock
    private AlipayBillItemConverter itemConverter;

    private static final String TEST_FILE_PATH = "testdata/alipay/支付宝交易明细(********-********).csv";
    private File testFile;

    @BeforeEach
    public void setUp() {
        // 由于中文文件名在测试环境中可能有编码问题，我们创建一个模拟文件
        // 在实际测试中，我们主要测试解析器的逻辑，而不是文件读取
        testFile = new File("test-alipay-bill.csv");
    }

    @Test
    public void testGetSupportedBillType() {
        // 测试获取支持的账单类型
        String billType = parser.getSupportedBillType();
        assertEquals("ALIPAY", billType);
    }

    @Test
    public void testCanParse() {
        // 测试能否解析支付宝账单文件（基于文件名）
        // 使用实际存在的测试文件
        URL resource = getClass().getClassLoader().getResource("testdata/alipay/支付宝交易明细(********-********).csv");
        assertNotNull(resource);

        File alipayFile = null;
        try {
            // 使用URI来正确处理中文文件名
            alipayFile = new File(resource.toURI());
        } catch (Exception e) {
            // 如果URI转换失败，使用原来的方法
            alipayFile = new File(resource.getFile());
        }
        assertTrue(parser.canParse(alipayFile));

        // 测试包含支付宝关键词的文件名
        File tempDir = new File(System.getProperty("java.io.tmpdir"));
        File alipayFile2 = new File(tempDir, "alipay_bill.csv");
        try {
            alipayFile2.createNewFile();
            assertTrue(parser.canParse(alipayFile2));
        } catch (Exception e) {
            // 如果无法创建临时文件，跳过这个测试
        } finally {
            if (alipayFile2.exists()) {
                alipayFile2.delete();
            }
        }

        // 测试不能解析的文件
        File nonAlipayFile = new File(tempDir, "test.txt");
        try {
            nonAlipayFile.createNewFile();
            assertFalse(parser.canParse(nonAlipayFile));
        } catch (Exception e) {
            // 如果无法创建临时文件，跳过这个测试
        } finally {
            if (nonAlipayFile.exists()) {
                nonAlipayFile.delete();
            }
        }

        // 测试null文件
        assertFalse(parser.canParse(null));
    }

    @Test
    public void testParseBillFileInfo() {
        // 准备Mock数据
        BillFileInfoDO mockFileInfo = BillFileInfoDO.builder()
                .type("ALIPAY")
                .originalName(testFile.getName())
                .nickname("米似金")
                .build();
        
        when(fileInfoParser.parse(any(File.class))).thenReturn(mockFileInfo);
        
        // 执行测试
        BillFileInfoDO result = parser.parseBillFileInfo(testFile);
        
        // 验证结果
        assertNotNull(result);
        assertEquals("ALIPAY", result.getType());
        assertEquals(testFile.getName(), result.getOriginalName());
        assertEquals("米似金", result.getNickname());
    }

    @Test
    public void testParseBillDetails() {
        // 准备Mock数据
        List<AlipayCsvData> mockDetails = List.of(
                createMockAlipayCsvData("2025/6/27 20:00", "餐饮美食", "支出", "43.36"),
                createMockAlipayCsvData("2025/6/26 11:54", "保险", "支出", "77.85")
        );
        
        // Mock detailParser的行为 - parseWithBatchProcessor是void方法，不需要返回值
        
        // 由于parseWithBatchProcessor是void方法，我们需要用不同的方式测试
        // 这里主要测试方法不抛异常
        assertDoesNotThrow(() -> {
            List<AlipayCsvData> result = parser.parseBillDetails(testFile);
            assertNotNull(result);
        });
    }

    @Test
    public void testConvertToAccountBookItems() {
        // 准备测试数据
        List<AlipayCsvData> billDetails = List.of(
                createMockAlipayCsvData("2025/6/27 20:00", "餐饮美食", "支出", "43.36"),
                createMockAlipayCsvData("2025/6/26 11:54", "保险", "支出", "77.85")
        );
        
        List<AccountBookItemCreateReqDTO> mockAccountBookItems = List.of(
                createMockAccountBookItem(1L, false, "43.36"),
                createMockAccountBookItem(1L, false, "77.85")
        );
        
        when(itemConverter.convert(any(), any(), any())).thenReturn(mockAccountBookItems);
        
        // 执行测试
        List<AccountBookItemCreateReqDTO> result = parser.convertToAccountBookItems(billDetails, 1L, 1L);
        
        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
    }

    /**
     * 创建Mock的支付宝CSV数据
     */
    private AlipayCsvData createMockAlipayCsvData(String tradeTime, String category, String inOrOut, String amount) {
        AlipayCsvData data = new AlipayCsvData();
        data.setTradeTime(tradeTime);
        data.setTradeCategory(category);
        data.setInOrOut(inOrOut);
        data.setAmount(amount);
        data.setCounterparty("测试商户");
        data.setGoodsDescription("测试商品");
        data.setPaymentMethod("测试支付方式");
        data.setTradeStatus("交易成功");
        return data;
    }

    /**
     * 创建Mock的账本项目
     */
    private AccountBookItemCreateReqDTO createMockAccountBookItem(Long userId, Boolean type, String amount) {
        AccountBookItemCreateReqDTO dto = new AccountBookItemCreateReqDTO();
        dto.setUserId(userId);
        dto.setType(type);
        dto.setSourceType("导入");
        return dto;
    }
}
