package cn.iocoder.yudao.module.importer.controller.app;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.test.core.ut.BaseDbUnitTest;
import cn.iocoder.yudao.module.importer.controller.app.vo.*;
import cn.iocoder.yudao.module.importer.dal.dataobject.importtask.ImportTaskDO;
import cn.iocoder.yudao.module.importer.enums.ImportTaskStatusEnum;
import cn.iocoder.yudao.module.importer.mq.producer.ImporterProducer;
import cn.iocoder.yudao.module.importer.service.importtask.ImportTaskService;
import cn.iocoder.yudao.module.importer.service.password.PasswordVerificationService;
import cn.iocoder.yudao.module.importer.service.security.SignatureVerificationService;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

import jakarta.annotation.Resource;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * {@link ImportController} 的单元测试类
 */
@Import({ImportController.class})
public class ImportControllerTest extends BaseDbUnitTest {

    @Resource
    private ImportController importController;

    @MockBean
    private ImportTaskService importTaskService;

    @MockBean
    private SignatureVerificationService signatureVerificationService;

    @MockBean
    private PasswordVerificationService passwordVerificationService;

    @MockBean
    private ImporterProducer importerProducer;

    @Test
    public void testCreateImportTask() {
        // 准备参数
        CreateImportTaskReqVO reqVO = new CreateImportTaskReqVO();
        reqVO.setAccountBookId(100L);

        // mock SecurityFrameworkUtils.getLoginUserId()
        try (var mockStatic = mockStatic(cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.class)) {
            mockStatic.when(cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils::getLoginUserId)
                    .thenReturn(1L);

            // mock 方法
            String taskId = "test-task-id";
            when(importTaskService.createImportTask(anyLong(), eq(100L))).thenReturn(taskId);
            
            ImportTaskDO mockTask = new ImportTaskDO();
            mockTask.setTaskId(taskId);
            mockTask.setUniqueEmail("<EMAIL>");
            when(importTaskService.getByTaskId(taskId)).thenReturn(mockTask);

            // 调用
            CommonResult<CreateImportTaskRespVO> result = importController.createImportTask(reqVO);

            // 断言
            assertTrue(result.isSuccess());
            assertNotNull(result.getData());
            assertEquals(taskId, result.getData().getTaskId());
            assertEquals(mockTask.getUniqueEmail(), result.getData().getUniqueEmail());

            // 验证调用
            verify(importTaskService).createImportTask(eq(1L), eq(100L));
            verify(importTaskService).getByTaskId(taskId);
        }
    }



    @Test
    public void testSubmitPassword_Success() {
        // 准备参数
        String taskId = "test-task-id";
        SubmitPasswordReqVO reqVO = new SubmitPasswordReqVO();
        reqVO.setPassword("123456");

        // mock 数据
        ImportTaskDO mockTask = new ImportTaskDO();
        mockTask.setTaskId(taskId);
        mockTask.setStatus(ImportTaskStatusEnum.AWAITING_PASSWORD.getStatus());
        mockTask.setFilePath("/test/file/path.zip");

        // mock 方法
        when(importTaskService.getByTaskId(taskId)).thenReturn(mockTask);
        when(passwordVerificationService.quickVerifyZipPassword(anyString(), anyString()))
                .thenReturn(true);

        // 调用
        CommonResult<Boolean> result = importController.submitPassword(taskId, reqVO);

        // 断言
        assertTrue(result.isSuccess());
        assertTrue(result.getData());

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(passwordVerificationService).quickVerifyZipPassword("/test/file/path.zip", "123456");
        verify(importTaskService).updateUnzipPassword(taskId, "123456");
        verify(importerProducer).sendBillParsingMessage(taskId, "/test/file/path.zip", "123456");
    }

    @Test
    public void testSubmitPassword_TaskNotFound() {
        // 准备参数
        String taskId = "non-existent-task-id";
        SubmitPasswordReqVO reqVO = new SubmitPasswordReqVO();
        reqVO.setPassword("123456");

        // mock 方法
        when(importTaskService.getByTaskId(taskId)).thenReturn(null);

        // 调用
        CommonResult<Boolean> result = importController.submitPassword(taskId, reqVO);

        // 断言
        assertTrue(result.isSuccess());
        assertFalse(result.getData());

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(passwordVerificationService, never()).quickVerifyZipPassword(anyString(), anyString());
        verify(importerProducer, never()).sendBillParsingMessage(anyString(), anyString(), anyString());
    }

    @Test
    public void testSubmitPassword_WrongStatus() {
        // 准备参数
        String taskId = "test-task-id";
        SubmitPasswordReqVO reqVO = new SubmitPasswordReqVO();
        reqVO.setPassword("123456");

        // mock 数据
        ImportTaskDO mockTask = new ImportTaskDO();
        mockTask.setTaskId(taskId);
        mockTask.setStatus(ImportTaskStatusEnum.COMPLETED.getStatus()); // 错误状态

        // mock 方法
        when(importTaskService.getByTaskId(taskId)).thenReturn(mockTask);

        // 调用
        CommonResult<Boolean> result = importController.submitPassword(taskId, reqVO);

        // 断言
        assertTrue(result.isSuccess());
        assertFalse(result.getData());

        // 验证调用
        verify(importTaskService).getByTaskId(taskId);
        verify(passwordVerificationService, never()).quickVerifyZipPassword(anyString(), anyString());
        verify(importerProducer, never()).sendBillParsingMessage(anyString(), anyString(), anyString());
    }

}