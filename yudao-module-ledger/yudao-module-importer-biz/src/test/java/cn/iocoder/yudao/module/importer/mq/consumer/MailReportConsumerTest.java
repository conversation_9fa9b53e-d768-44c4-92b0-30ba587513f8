package cn.iocoder.yudao.module.importer.mq.consumer;

import cn.iocoder.yudao.module.importer.mq.message.MailReportMessage;
import cn.iocoder.yudao.module.importer.service.email.EmailProcessingService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

/**
 * {@link MailReportConsumer} 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class MailReportConsumerTest {

    @Mock
    private EmailProcessingService emailProcessingService;

    @InjectMocks
    private MailReportConsumer mailReportConsumer;

    @Test
    public void testOnMessage_Success() {
        // 准备测试数据
        MailReportMessage message = new MailReportMessage();
        message.setObjectKey("emails/test123/email.eml");
        message.setSender("<EMAIL>");
        message.setRecipient("<EMAIL>");
        message.setEmailId("email-123");
        message.setReceivedTime(LocalDateTime.now());

        // 执行测试
        mailReportConsumer.onMessage(message);

        // 验证调用
        verify(emailProcessingService).processReportedEmail(message);
    }

    @Test
    public void testOnMessage_ProcessingException() {
        // 准备测试数据
        MailReportMessage message = new MailReportMessage();
        message.setObjectKey("emails/test123/email.eml");
        message.setSender("<EMAIL>");
        message.setRecipient("<EMAIL>");
        message.setEmailId("email-123");
        message.setReceivedTime(LocalDateTime.now());

        // Mock服务调用抛出异常
        RuntimeException exception = new RuntimeException("处理失败");
        doThrow(exception).when(emailProcessingService).processReportedEmail(message);

        // 执行测试并验证异常被重新抛出
        assertThrows(RuntimeException.class, () -> mailReportConsumer.onMessage(message));

        // 验证调用
        verify(emailProcessingService).processReportedEmail(message);
    }

}
