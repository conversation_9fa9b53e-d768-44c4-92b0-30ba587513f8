package cn.iocoder.yudao.module.importer.controller.app;

import cn.iocoder.yudao.module.importer.controller.app.vo.ValidateEmailReqVO;
import cn.iocoder.yudao.module.importer.controller.app.vo.ValidateEmailRespVO;
import cn.iocoder.yudao.module.importer.service.mailserver.MailServerService;
import cn.iocoder.yudao.module.importer.service.security.SignatureVerificationService;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.setup.MockMvcBuilders;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * {@link MailServerController} 的单元测试类
 */
@ExtendWith(MockitoExtension.class)
public class MailServerControllerTest {

    @Mock
    private MailServerService mailServerService;

    @Mock
    private SignatureVerificationService signatureVerificationService;

    @InjectMocks
    private MailServerController mailServerController;

    private MockMvc mockMvc;
    private ObjectMapper objectMapper;

    @BeforeEach
    public void setUp() {
        mockMvc = MockMvcBuilders.standaloneSetup(mailServerController).build();
        objectMapper = new ObjectMapper();
    }

    @Test
    public void testValidateEmail_Success() throws Exception {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";
        String signature = "test-signature";
        String timestamp = "1640995200";



        // Mock服务调用
        when(signatureVerificationService.verifySignature(eq(signature), eq(timestamp), anyString()))
                .thenReturn(true);
        when(mailServerService.validateEmail(sender, recipient))
                .thenReturn(ValidateEmailRespVO.success());

        // 构建请求体
        ValidateEmailReqVO reqVO = new ValidateEmailReqVO();
        reqVO.setSender(sender);
        reqVO.setRecipient(recipient);
        reqVO.setSignature(signature);
        reqVO.setTimestamp(timestamp);

        // 执行测试
        mockMvc.perform(post("/ledger/mail-server/validate-email")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.valid").value(true));
    }

    @Test
    public void testValidateEmail_InvalidSignature() throws Exception {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";
        String signature = "invalid-signature";
        String timestamp = "1640995200";

        // Mock服务调用 - 签名验证失败
        when(signatureVerificationService.verifySignature(eq(signature), eq(timestamp), anyString()))
                .thenReturn(false);

        // 构建请求体
        ValidateEmailReqVO reqVO = new ValidateEmailReqVO();
        reqVO.setSender(sender);
        reqVO.setRecipient(recipient);
        reqVO.setSignature(signature);
        reqVO.setTimestamp(timestamp);

        // 执行测试
        mockMvc.perform(post("/ledger/mail-server/validate-email")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.valid").value(false))
                .andExpect(jsonPath("$.data.errorMessage").value("签名验证失败"));
    }

    @Test
    public void testValidateEmail_ServiceFailure() throws Exception {
        // 准备测试数据
        String sender = "<EMAIL>";
        String recipient = "<EMAIL>";
        String signature = "test-signature";
        String timestamp = "1640995200";

        // Mock服务调用
        when(signatureVerificationService.verifySignature(eq(signature), eq(timestamp), anyString()))
                .thenReturn(true);
        when(mailServerService.validateEmail(sender, recipient))
                .thenReturn(ValidateEmailRespVO.fail("不支持的发件人邮箱：" + sender));

        // 构建请求体
        ValidateEmailReqVO reqVO = new ValidateEmailReqVO();
        reqVO.setSender(sender);
        reqVO.setRecipient(recipient);
        reqVO.setSignature(signature);
        reqVO.setTimestamp(timestamp);

        // 执行测试
        mockMvc.perform(post("/ledger/mail-server/validate-email")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(reqVO)))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data.valid").value(false))
                .andExpect(jsonPath("$.data.errorMessage").value("不支持的发件人邮箱：" + sender));
    }

}
